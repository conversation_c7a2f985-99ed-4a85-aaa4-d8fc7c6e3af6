import React, { useState } from 'react';
import Avatar from '../Common/Avatar';
import { AlertCircle, CheckCircle, Info, RefreshCw, Copy, ExternalLink } from 'lucide-react';

const AvatarDebug: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [imageLoadStatus, setImageLoadStatus] = useState<Record<string, 'loading' | 'success' | 'error'>>({});

  // Enhanced test URLs for different scenarios including common user image sources
  const testCases = [
    {
      name: 'Valid Image URL',
      src: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      userName: '<PERSON>',
      description: 'Standard external image URL'
    },
    {
      name: 'Google Profile Image (Valid)',
      src: 'https://lh3.googleusercontent.com/a/ACg8ocKxVxvJGKwQvzCTwXQs8QGkqp1QvzCTwXQs8QGkqp1Q=s96-c',
      userName: 'Google User',
      description: 'Properly formatted Google profile image'
    },
    {
      name: 'Google Profile Image (Invalid)',
      src: 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      userName: 'Google User Invalid',
      description: 'Invalid Google profile image URL'
    },
    {
      name: 'Gravatar Image',
      src: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50?s=200',
      userName: 'Gravatar User',
      description: 'Gravatar profile image'
    },
    {
      name: 'Invalid URL',
      src: 'https://invalid-url.com/avatar.jpg',
      userName: 'Invalid User',
      description: 'Non-existent domain'
    },
    {
      name: 'No Image URL',
      src: undefined,
      userName: 'No Image User',
      description: 'Undefined image source'
    },
    {
      name: 'Empty String URL',
      src: '',
      userName: 'Empty URL User',
      description: 'Empty string image source'
    },
    {
      name: 'Malformed URL',
      src: 'not-a-valid-url',
      userName: 'Malformed User',
      description: 'Invalid URL format'
    }
  ];

  // Mock user data similar to what might come from the backend
  const mockUserData = [
    {
      userId: '1',
      name: 'Johnson',
      email: '<EMAIL>',
      photoUrl: 'https://lh3.googleusercontent.com/a/ACg8ocKxVxvJGKwQvzCTwXQs8QGkqp1QvzCTwXQs8QGkqp1Q=s96-c',
      roles: ['user']
    },
    {
      userId: '2',
      name: 'Kaung Kaung',
      email: '<EMAIL>',
      photoUrl: '', // Empty photo URL
      roles: ['cj']
    },
    {
      userId: '3',
      name: 'Kaung Kaung Thant Linn 123',
      email: '<EMAIL>',
      photoUrl: 'https://invalid-url.com/avatar.jpg', // Invalid URL
      roles: ['admin']
    },
    {
      userId: '4',
      name: 'Kaung Linn',
      email: '<EMAIL>',
      photoUrl: undefined, // Undefined photo URL
      roles: ['user']
    }
  ];

  const handleImageTest = (url: string) => {
    if (!url) return;

    setImageLoadStatus(prev => ({ ...prev, [url]: 'loading' }));

    const img = new Image();
    img.onload = () => {
      setImageLoadStatus(prev => ({ ...prev, [url]: 'success' }));
    };
    img.onerror = () => {
      setImageLoadStatus(prev => ({ ...prev, [url]: 'error' }));
    };
    img.src = url;
  };

  const getStatusIcon = (url: string) => {
    const status = imageLoadStatus[url];
    switch (status) {
      case 'loading':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-6xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Avatar Component Debug & Analysis</h2>
        <p className="text-gray-600">Comprehensive testing tool for user avatar display issues</p>
      </div>

      {/* Test Cases */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-700 flex items-center">
          <AlertCircle className="w-5 h-5 mr-2 text-blue-500" />
          Test Cases
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {testCases.map((testCase, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{testCase.name}</h4>
                {testCase.src && (
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(testCase.src)}
                    <button
                      onClick={() => handleImageTest(testCase.src!)}
                      className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                    >
                      Test
                    </button>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-3 mb-3">
                <Avatar
                  src={testCase.src}
                  name={testCase.userName}
                  size="lg"
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium">{testCase.userName}</div>
                  <div className="text-xs text-gray-500">{testCase.description}</div>
                  <div className="text-xs text-gray-400 break-all mt-1">
                    {testCase.src || 'No URL'}
                  </div>
                </div>
              </div>
              {testCase.src && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => copyToClipboard(testCase.src!)}
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Copy URL
                  </button>
                  <a
                    href={testCase.src}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center"
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Open
                  </a>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Mock User Data Simulation */}
      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold text-gray-700 flex items-center mb-4">
          <Info className="w-5 h-5 mr-2 text-green-500" />
          Mock User Data (Simulating Backend Response)
        </h3>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> This simulates the user data structure that would come from your backend API.
            The issues you're seeing in the user management interface are likely due to invalid or missing photoUrl values.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mockUserData.map((user, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-3">
                <Avatar
                  src={user.photoUrl}
                  name={user.name}
                  size="lg"
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium">{user.name}</div>
                  <div className="text-xs text-gray-500">{user.email}</div>
                  <div className="text-xs text-blue-600">Role: {user.roles.join(', ')}</div>
                </div>
              </div>
              <div className="text-xs bg-gray-50 p-2 rounded">
                <strong>photoUrl:</strong> {user.photoUrl || 'null/undefined'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Custom URL Test */}
      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold text-gray-700 flex items-center mb-4">
          <RefreshCw className="w-5 h-5 mr-2 text-purple-500" />
          Custom URL Test
        </h3>
        <div className="flex items-center space-x-4 mb-4">
          <input
            type="url"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            placeholder="Enter image URL to test..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <button
            onClick={() => handleImageTest(testUrl)}
            disabled={!testUrl}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Test
          </button>
          <button
            onClick={() => setTestUrl('')}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            Clear
          </button>
        </div>
        {testUrl && (
          <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
            <Avatar
              src={testUrl}
              name="Test User"
              size="lg"
            />
            <div className="flex-1">
              <div className="text-sm font-medium">Test User</div>
              <div className="text-xs text-gray-500 break-all">{testUrl}</div>
              <div className="flex items-center mt-2">
                {getStatusIcon(testUrl)}
                <span className="text-xs ml-2">
                  {imageLoadStatus[testUrl] === 'loading' && 'Testing image...'}
                  {imageLoadStatus[testUrl] === 'success' && 'Image loads successfully'}
                  {imageLoadStatus[testUrl] === 'error' && 'Image failed to load'}
                  {!imageLoadStatus[testUrl] && 'Click Test to check image'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Size Variations */}
      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Size Variations</h3>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <Avatar
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
              name="Small User"
              size="sm"
            />
            <div className="text-xs mt-1">Small</div>
          </div>
          <div className="text-center">
            <Avatar
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
              name="Medium User"
              size="md"
            />
            <div className="text-xs mt-1">Medium</div>
          </div>
          <div className="text-center">
            <Avatar
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
              name="Large User"
              size="lg"
            />
            <div className="text-xs mt-1">Large</div>
          </div>
          <div className="text-center">
            <Avatar
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
              name="XL User"
              size="xl"
            />
            <div className="text-xs mt-1">Extra Large</div>
          </div>
        </div>
      </div>

      {/* Fallback Cases */}
      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Fallback Cases</h3>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <Avatar name="John Doe" size="lg" />
            <div className="text-xs mt-1">Initials</div>
          </div>
          <div className="text-center">
            <Avatar name="A" size="lg" />
            <div className="text-xs mt-1">Single Letter</div>
          </div>
          <div className="text-center">
            <Avatar name="" size="lg" />
            <div className="text-xs mt-1">No Name</div>
          </div>
          <div className="text-center">
            <Avatar size="lg" />
            <div className="text-xs mt-1">No Props</div>
          </div>
        </div>
      </div>

      {/* Troubleshooting Guide */}
      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold text-gray-700 flex items-center mb-4">
          <AlertCircle className="w-5 h-5 mr-2 text-orange-500" />
          Troubleshooting Guide
        </h3>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Common Issues & Solutions:</h4>
          <ul className="text-sm text-blue-800 space-y-2">
            <li><strong>1. Images showing initials instead of photos:</strong> Check if photoUrl field in user data is valid and accessible</li>
            <li><strong>2. Google profile images not loading:</strong> Ensure proper Google OAuth scope and valid image URLs</li>
            <li><strong>3. CORS errors:</strong> External image domains may block cross-origin requests</li>
            <li><strong>4. Backend connectivity:</strong> Verify API endpoint is running and accessible</li>
            <li><strong>5. Image optimization:</strong> Large images may fail to load - use optimized URLs</li>
          </ul>
        </div>
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-900 mb-2">Recommended Actions:</h4>
          <ul className="text-sm text-green-800 space-y-2">
            <li>• Check browser console for image loading errors</li>
            <li>• Verify user data structure matches expected format</li>
            <li>• Test image URLs directly in browser</li>
            <li>• Implement proper error handling and fallbacks</li>
            <li>• Consider using a CDN for better image delivery</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AvatarDebug;
