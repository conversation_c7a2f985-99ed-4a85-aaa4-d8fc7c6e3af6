import React, { useState, useEffect } from 'react';
import { User, AlertCircle } from 'lucide-react';
import {
  isValidImageUrl,
  optimizeAvatarUrl,
  getInitials,
  getAvatarBackgroundColor,
  getAvatarTextColor
} from '../../utils/avatarUtils';

interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showDebugInfo?: boolean; // For debugging purposes
  onImageError?: (error: string) => void; // Callback for error handling
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'md',
  className = '',
  showDebugInfo = false,
  onImageError
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(!!src);
  const [debugInfo, setDebugInfo] = useState<string>('');

  // Optimize the image URL for better performance and reliability
  const optimizedSrc = src ? optimizeAvatarUrl(src) : undefined;

  // Reset states when src changes
  useEffect(() => {
    if (src) {
      setImageError(false);
      setImageLoading(true);
      setDebugInfo('Loading image...');
    } else {
      setImageLoading(false);
      setDebugInfo('No image URL provided');
    }
  }, [src]);



  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 12,
    md: 16,
    lg: 20,
    xl: 24
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
    setDebugInfo('Image loaded successfully');
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setImageError(true);
    setImageLoading(false);

    const errorMessage = `Failed to load image: ${optimizedSrc}`;
    setDebugInfo(errorMessage);

    // Call the error callback if provided
    if (onImageError) {
      onImageError(errorMessage);
    }

    // Log error for debugging (only in development)
    if (process.env.NODE_ENV === 'development' && showDebugInfo) {
      console.warn('Avatar image failed to load:', {
        originalSrc: src,
        optimizedSrc,
        name,
        error: event
      });
    }
  };

  const baseClasses = `${sizeClasses[size]} rounded-full object-cover ${className}`;

  // Get dynamic background and text colors based on name
  const backgroundColor = getAvatarBackgroundColor(name);
  const textColor = getAvatarTextColor(backgroundColor);

  // Show image if src exists and no error occurred
  if (src && !imageError) {
    return (
      <div className="relative">
        <img
          src={optimizedSrc || src}
          alt={alt || name || 'User avatar'}
          className={baseClasses}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
        {imageLoading && (
          <div className={`${baseClasses} ${backgroundColor} animate-pulse flex items-center justify-center absolute inset-0`}>
            <User size={iconSizes[size]} className={textColor.replace('text-', 'text-').replace('-600', '-400')} />
          </div>
        )}
        {showDebugInfo && (
          <div className="absolute -bottom-6 left-0 text-xs bg-black text-white px-1 rounded z-10 whitespace-nowrap">
            {debugInfo}
          </div>
        )}
      </div>
    );
  }

  // Show initials if name is available
  if (name && getInitials(name)) {
    return (
      <div className="relative">
        <div className={`${baseClasses} ${backgroundColor} flex items-center justify-center ${textColor} font-medium`}>
          <span className={`text-${size === 'sm' ? 'xs' : size === 'md' ? 'sm' : 'base'}`}>
            {getInitials(name)}
          </span>
        </div>
        {showDebugInfo && (
          <div className="absolute -bottom-6 left-0 text-xs bg-black text-white px-1 rounded z-10 whitespace-nowrap">
            {debugInfo || 'Showing initials fallback'}
          </div>
        )}
      </div>
    );
  }

  // Fallback to default user icon
  return (
    <div className="relative">
      <div className={`${baseClasses} ${backgroundColor} flex items-center justify-center`}>
        <User size={iconSizes[size]} className={textColor} />
      </div>
      {showDebugInfo && (
        <div className="absolute -bottom-6 left-0 text-xs bg-black text-white px-1 rounded z-10 whitespace-nowrap">
          {debugInfo || 'Showing default icon fallback'}
        </div>
      )}
    </div>
  );
};

export default Avatar;