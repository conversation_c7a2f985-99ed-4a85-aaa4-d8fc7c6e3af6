import React, { useState } from 'react';
import { Search, AlertCircle, CheckCircle, X, Copy, ExternalLink, RefreshCw } from 'lucide-react';
import Avatar from '../Common/Avatar';
import { extractPhotoUrl, optimizeAvatarUrl } from '../../utils/avatarUtils';

interface UserImageDebugProps {
  className?: string;
}

// Exact user data from your screenshot
const actualUserData = [
  {
    userId: '1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    photoUrl: '', // This is likely why you're seeing initials
    roles: ['cj']
  },
  {
    userId: '2',
    name: '<PERSON><PERSON> Than<PERSON> Linn 123',
    email: '<EMAIL>',
    photoUrl: '', // This is likely why you're seeing initials
    roles: ['admin']
  },
  {
    userId: '3',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    photoUrl: '', // This is likely why you're seeing initials
    roles: ['user']
  }
];

const UserImageDebug: React.FC<UserImageDebugProps> = ({ className = '' }) => {
  const [testUrl, setTestUrl] = useState('');
  const [testResults, setTestResults] = useState<{[key: string]: 'loading' | 'success' | 'error'}>({});
  const [showDebugInfo, setShowDebugInfo] = useState(true);

  const testImageUrl = async (url: string, key: string) => {
    if (!url) {
      setTestResults(prev => ({ ...prev, [key]: 'error' }));
      return;
    }

    setTestResults(prev => ({ ...prev, [key]: 'loading' }));

    try {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = url;
      });

      setTestResults(prev => ({ ...prev, [key]: 'success' }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [key]: 'error' }));
    }
  };

  const handleTestUrl = () => {
    if (testUrl) {
      testImageUrl(testUrl, 'manual-test');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const openUrl = (url: string) => {
    window.open(url, '_blank');
  };

  const getStatusIcon = (status: 'loading' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <X className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className={`max-w-6xl mx-auto p-6 ${className}`}>
      <div className="bg-white rounded-lg shadow-lg">
        <div className="border-b border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Search className="w-6 h-6 mr-3 text-blue-600" />
            User Image Debug Tool
          </h2>
          <p className="text-gray-600 mt-2">
            Debug tool for investigating why user profile images are showing initials instead of photos
          </p>
        </div>

        <div className="p-6">
          {/* Problem Analysis */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-red-800 flex items-center mb-2">
              <AlertCircle className="w-5 h-5 mr-2" />
              Problem Identified
            </h3>
            <p className="text-red-700 mb-2">
              <strong>Root Cause:</strong> All users in your system have empty or invalid photoUrl values.
            </p>
            <p className="text-red-600 text-sm">
              The Avatar component is working correctly by showing initials when no valid image URL is provided.
            </p>
          </div>

          {/* Your Actual User Data */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-700 flex items-center mb-4">
              <AlertCircle className="w-5 h-5 mr-2 text-orange-500" />
              Your Actual User Data (From Screenshot)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {actualUserData.map((user, index) => {
                const extractedUrl = extractPhotoUrl(user);
                const optimizedUrl = extractedUrl ? optimizeAvatarUrl(extractedUrl) : undefined;
                const testKey = `user-${index}`;

                return (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <Avatar
                        src={extractedUrl}
                        name={user.name}
                        size="lg"
                        showDebugInfo={showDebugInfo}
                        onImageError={(error) => console.log(`Avatar error for ${user.name}:`, error)}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{user.name}</p>
                        <p className="text-sm text-gray-500 truncate">{user.email}</p>
                        <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                          {user.roles[0].toUpperCase()}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Original photoUrl:</span>
                        <span className="ml-2 text-red-600">
                          {user.photoUrl || '(empty/null)'}
                        </span>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700">Extracted URL:</span>
                        <span className="ml-2 text-red-600">
                          {extractedUrl || '(none)'}
                        </span>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700">Status:</span>
                        <span className="ml-2 flex items-center">
                          {getStatusIcon(testResults[testKey])}
                          <span className="ml-1 text-red-600">No valid image URL</span>
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Manual URL Testing */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-700 flex items-center mb-4">
              <Search className="w-5 h-5 mr-2 text-blue-500" />
              Test Image URL Manually
            </h3>
            <div className="flex space-x-2 mb-4">
              <input
                type="text"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                placeholder="Enter image URL to test..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleTestUrl}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Test URL
              </button>
            </div>

            {testUrl && (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Avatar
                    src={testUrl}
                    name="Test User"
                    size="lg"
                    showDebugInfo={showDebugInfo}
                  />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">Test User</p>
                    <p className="text-sm text-gray-500"><EMAIL></p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(testResults['manual-test'])}
                    <button
                      onClick={() => copyToClipboard(testUrl)}
                      className="p-1 text-gray-500 hover:text-gray-700"
                      title="Copy URL"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => openUrl(testUrl)}
                      className="p-1 text-gray-500 hover:text-gray-700"
                      title="Open URL"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <strong>URL:</strong> {testUrl}
                </div>
              </div>
            )}
          </div>

          {/* Debug Controls */}
          <div className="mb-6">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showDebugInfo}
                onChange={(e) => setShowDebugInfo(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Show debug tooltips on avatars</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserImageDebug;